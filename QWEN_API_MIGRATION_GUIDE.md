# Qwen API 迁移指南

## 概述

本指南说明了如何将 `parallel_generate-refgpt-fact.py` 从 OpenAI API 迁移到 Qwen API。

## 主要修改

### 1. 请求头格式修改

**修改前 (OpenAI):**
```python
request_header.update({'api-key': api['api-key']})
```

**修改后 (<PERSON>wen):**
```python
request_header.update({'Authorization': f"Bearer {api['api-key']}"})
```

### 2. 添加模型参数

**修改前:**
```python
decoding_args = dict(
    temperature=1.0,
    n=1,
    max_tokens=args.max_tokens,  
    top_p=1.0,
    stop=["\n20", "20.", "20."],
)
```

**修改后:**
```python
decoding_args = dict(
    model=args.model,  # 新增模型参数
    temperature=1.0,
    n=1,
    max_tokens=args.max_tokens,  
    top_p=1.0,
    stop=["\n20", "20.", "20."],
)
```

### 3. 添加命令行参数

新增了 `--model` 参数：
```python
parser.add_argument("--model", default="qwen-turbo",
                    help="model name to use for API requests (e.g., qwen-turbo, qwen-plus, qwen-max)")
```

### 4. 更新 API 配置文件

**修改前:**
```json
{"api-key": "your-key", "request_url": "https://api.openai.com/v1/engines/davinci/completions"}
```

**修改后:**
```json
{"api-key": "your-key", "request_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"}
```

## 可用的 Qwen 模型

| 模型名称 | 描述 | 适用场景 |
|---------|------|----------|
| `qwen-turbo` | 通用模型，速度快，成本低 | 日常对话、简单任务 |
| `qwen-plus` | 增强模型，能力更强 | 复杂推理、专业内容 |
| `qwen-max` | 最强模型，推理能力最佳 | 高难度任务、创意写作 |
| `qwen-max-longcontext` | 长文本模型 | 长文档处理 |

## 使用示例

### 基本使用
```bash
python3 parallel_generate-refgpt-fact.py \
    --save_filepath output_qwen.jsonl \
    --reference_filepaths sample_references.jsonl \
    --api_config api_config.jsonl \
    --language zh \
    --num_chat_to_generate 5 \
    --model qwen-turbo
```

### 使用更强的模型
```bash
python3 parallel_generate-refgpt-fact.py \
    --save_filepath output_qwen_plus.jsonl \
    --reference_filepaths sample_references.jsonl \
    --api_config api_config.jsonl \
    --language zh \
    --num_chat_to_generate 5 \
    --model qwen-plus
```

### 生成英文对话
```bash
python3 parallel_generate-refgpt-fact.py \
    --save_filepath output_qwen_en.jsonl \
    --reference_filepaths sample_references.jsonl \
    --api_config api_config.jsonl \
    --language en \
    --num_chat_to_generate 5 \
    --model qwen-turbo
```

## 注意事项

1. **文本长度要求**: 确保参考文本足够长，至少要达到 `0.8 * total_word_count`
2. **API 额度**: 确保您的 Qwen API 账户有足够的额度
3. **速率限制**: 根据您的 API 套餐调整 `--max_requests_per_minute` 和 `--max_tokens_per_minute` 参数
4. **模型选择**: 根据任务复杂度选择合适的模型

## 测试

运行以下命令测试 API 配置：
```bash
python3 test_qwen_api.py
```

如果看到 "✅ Qwen API 配置正确，可以正常使用！" 说明配置成功。

## 故障排除

### 常见问题

1. **API 调用失败**: 检查 API key 是否正确，网络连接是否正常
2. **没有生成对话**: 检查参考文本是否足够长
3. **模型不存在**: 确认使用的模型名称是否正确

### 调试技巧

使用详细日志模式：
```bash
python3 parallel_generate-refgpt-fact.py \
    --logging_level 10 \
    [其他参数...]
```

## 文件清单

- `parallel_generate-refgpt-fact.py` - 主脚本（已修改）
- `api_config.jsonl` - API 配置文件（已更新）
- `test_qwen_api.py` - API 测试脚本（新增）
- `example_usage.py` - 使用示例脚本（新增）
- `long_sample.jsonl` - 长文本示例（新增）
- `QWEN_API_MIGRATION_GUIDE.md` - 本指南（新增）
