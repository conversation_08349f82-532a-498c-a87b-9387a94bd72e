# 增强版语音评估数据集生成器使用指南

## 🎯 功能概述

这个工具基于构音评估表和Qwen LLM，参考RefGPT的高效并发处理方法，生成高质量的语音评估数据集。

### 核心特点
- **高效并发**: 参考RefGPT的异步处理架构，支持高并发API调用
- **专业准确**: 基于52个标准化测试词汇和临床评估标准
- **真实场景**: 模拟真实的家长咨询场景
- **质量控制**: 多层验证和重试机制确保数据质量

## 🚀 快速开始

### 1. 测试模式（推荐先运行）
```bash
python enhanced_speech_assessment_generator.py --test_mode
```
这将生成5条测试记录，验证API配置和功能是否正常。

### 2. 生成1000条数据（默认）
```bash
python enhanced_speech_assessment_generator.py
```

### 3. 自定义参数
```bash
python enhanced_speech_assessment_generator.py \
    --num_records 2000 \
    --output_file my_dataset.jsonl \
    --max_concurrent 15 \
    --api_config api_config.jsonl
```

## 📋 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--num_records` | 1000 | 生成记录数量 |
| `--output_file` | enhanced_speech_assessment_dataset.jsonl | 输出文件名 |
| `--max_concurrent` | 10 | 最大并发请求数 |
| `--api_config` | api_config.jsonl | API配置文件路径 |
| `--test_mode` | False | 测试模式（生成5条记录） |

## 📊 生成的数据格式

### 完整记录结构
```json
{
  "record_id": "ESA_20250718_0001",
  "source": "enhanced_speech_assessment_llm",
  "created_at": "2025-07-18T14:30:22.123456",
  
  "child_info": {
    "age_months": 30,
    "age_description": "2岁6个月",
    "gender": "男孩",
    "development_stage": "语法发展期"
  },
  
  "assessment_case": {
    "target_word": "猪",
    "target_phoneme": "zh",
    "suspected_error_type": "替代",
    "parent_description": "我家男孩2岁6个月了，孩子说'猪'的时候，总是发成别的音，听起来不太对。我比较担心，这需要干预吗？",
    "concern_level": "需要注意"
  },
  
  "professional_assessment": "1. **问题判断**：轻度问题\n\n2. **问题分析**：孩子出现的是典型的卷舌音替代问题...",
  
  "dialogue": "<开始对话>\n<家长 1>: 我家男孩2岁6个月了...\n<专家 1>: 我来为您分析一下孩子的情况...\n<结束对话>",
  
  "metadata": {
    "generation_method": "llm_assisted_concurrent",
    "api_model": "qwen-turbo",
    "assessment_type": "parent_reported"
  }
}
```

## 🔧 技术特点

### 1. 高效并发处理
- 参考RefGPT的异步架构设计
- 支持可配置的并发数量
- 自动重试机制
- 实时进度监控

### 2. 专业评估体系
- 基于52个标准化测试词汇
- 4种主要构音错误类型（替代、省略、歪曲、增加）
- 4个年龄段的发展特征（12-18、18-24、24-36、36-48个月）
- 专业的评估和指导建议

### 3. 真实场景模拟
- 多样化的父母描述模板
- 年龄适宜的担心程度
- 自然的对话生成
- 个性化的专业建议

## 📈 性能优化

### 并发数量建议
- **测试环境**: 5-10个并发
- **生产环境**: 10-20个并发
- **高性能**: 20-50个并发（需要足够的API配额）

### 速度参考
- 单条记录平均耗时: 2-5秒
- 1000条记录预计用时: 5-15分钟（取决于并发数和网络状况）

## 🛠️ 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API配额是否充足

2. **并发数过高导致限流**
   - 降低`--max_concurrent`参数
   - 检查API的速率限制

3. **生成记录不完整**
   - 查看日志中的错误信息
   - 检查API响应格式是否正确

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -u enhanced_speech_assessment_generator.py --test_mode 2>&1 | tee debug.log
```

## 📝 数据质量保证

### 自动验证
- API响应格式检查
- 必要字段完整性验证
- 重试机制确保成功率

### 人工审核建议
1. 随机抽取5-10%的记录进行人工审核
2. 检查专业术语使用是否准确
3. 验证建议的实用性和可操作性

## 🎯 使用建议

### 数据集规模规划
- **开发测试**: 100-500条记录
- **模型训练**: 1000-5000条记录
- **生产部署**: 5000-10000条记录

### 质量控制流程
1. 先运行测试模式验证功能
2. 生成小批量数据进行质量检查
3. 确认无误后生成完整数据集
4. 定期更新和扩充数据集

---

通过这个增强版工具，您可以高效地生成大量高质量的语音评估数据，为训练专业的婴幼儿语音构音监测AI系统提供坚实的数据基础！
