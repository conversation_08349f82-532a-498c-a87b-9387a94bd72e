#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json

def merge_short_entries(input_file, output_file, min_words=500, max_entries=10):
    """将短条目合并成长文本"""

    with open(input_file, "r", encoding="utf8") as f:
        entries = [json.loads(line.strip()) for line in f if line.strip()]

    print(f"读取了 {len(entries)} 个条目")

    # 按类型分组
    titles = []
    paragraphs = []

    for entry in entries:
        desc = entry.get("desc", "").strip()
        entry_type = entry.get("type", "")

        if not desc:
            continue

        if entry_type == "title":
            titles.append(desc)
        elif entry_type == "paragraph":
            paragraphs.append(desc)

    print(f"找到 {len(titles)} 个标题和 {len(paragraphs)} 个段落")

    # 创建更有意义的合并
    merged_entries = []
    current_paragraphs = []
    current_word_count = 0

    # 确保有足够的标题
    if len(titles) < 5:
        titles.extend(["Articulation Therapy", "Speech Sound Development",
                      "Speech Therapy Techniques", "Language Development",
                      "Communication Skills"])

    title_index = 0

    for paragraph in paragraphs:
        # 清理段落中的URL和特殊字符
        cleaned_paragraph = paragraph.replace("https://", "").replace("http://", "")
        cleaned_paragraph = ' '.join(cleaned_paragraph.split())  # 规范化空格

        word_count = len(cleaned_paragraph.split())

        if current_word_count + word_count > 2000:  # 避免文本过长
            # 保存当前合并
            if current_word_count >= min_words:
                title = titles[title_index % len(titles)]
                title_index += 1

                merged_text = " ".join(current_paragraphs)
                merged_entries.append({
                    "title": title,
                    "desc": merged_text
                })
                print(f"创建条目: {title} ({current_word_count} 单词)")

                if len(merged_entries) >= max_entries:
                    break

            # 重置
            current_paragraphs = []
            current_word_count = 0

        current_paragraphs.append(cleaned_paragraph)
        current_word_count += word_count
    
    # 处理最后一组段落
    if current_word_count >= min_words and len(merged_entries) < max_entries:
        title = titles[title_index % len(titles)]
        merged_text = " ".join(current_paragraphs)
        merged_entries.append({
            "title": title,
            "desc": merged_text
        })
        print(f"创建条目: {title} ({current_word_count} 单词)")

    # 保存合并后的条目
    with open(output_file, "w", encoding="utf8") as f:
        for entry in merged_entries:
            f.write(json.dumps(entry, ensure_ascii=False) + "\n")

    print(f"\n✅ 成功创建 {len(merged_entries)} 个合并条目")
    print(f"保存到: {output_file}")

    return len(merged_entries)

if __name__ == "__main__":
    merge_count = merge_short_entries(
        "no_com_output-desc.jsonl",
        "merged_references.jsonl",
        min_words=500,
        max_entries=10
    )
