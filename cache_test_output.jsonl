{"api_input": {"model": "qwen-turbo", "temperature": 1.0, "n": 1, "max_tokens": 3072, "top_p": 1.0, "stop": ["\n20", "20.", "20."], "messages": [{"role": "system", "content": "要求你作为聊天机器人Assistant与人类Human进行多轮对话。对话是根据##提供信息##的内容开展的，并以#对话规划#的格式进行输出，以<start_chat>开始，以<end_chat>结束。"}, {"role": "user", "content": "##提供信息##\n人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。\n\n\n根据上面的##提供信息##内容以及主题，用中文扩写成一段多轮对话。对话要求你作为聊天机器人Assistant与人类Human进行对话, 并帮助解决Human所提出的要求。Human会以人类的语气对Assistant基于上面的信息（但对话中不能出现”根据以上信息“类似表达）提出多个不一样的问题/要求，且后一个问题/要求是基于前面的对话历史的进一步提问。对于Human提出的每个合理的问题/要求，Assistant要尽可能详细解答，提供更多说明或者举例子。对于Human的不合理（对社会有害、不道德、违法的）请求，Asistant会拒绝回答并解释不能回答的理由，同时给出合理的建议避免这样做。对话的内容要尽可能的符合人类的语言习惯，更加贴合人类日常对话。\n#对话规划#示例：“<start_chat><Human 1>:（字数要求：x字）XXX <Assistant 1>：（字数要求：x字）XXX <Human 2>：（字数要求：x字）XXX <Assistant 2>：（字数要求：x字）XXX <end_chat>”，其中“XXX”是对该角色的当前对话内容的要求，“（字数要求：x字）”是Human或者Assistant说话的最低字数要求。必须注意：对话以<start_chat>作为多轮对话的开始，<end_chat>作为多轮对话的结束。\n以下对话根据该#对话规划#并遵循规划里面的字数要求进行输出：“<start_chat><Human 1>：（字数要求：50字）以小孩子语气提问 <Assistant 1>：（字数要求：100字）以小孩听得懂方式回答 <Human 2>：（字数要求：100字）进一步提出问题 <Assistant 2>：（字数要求：200字）回答[+详细解释] <end_chat>”，共2轮对话。\n以下是2轮对话："}]}, "reference": "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。", "prompt": "\n根据上面的##提供信息##内容以及主题，用中文扩写成一段多轮对话。对话要求你作为聊天机器人Assistant与人类Human进行对话, 并帮助解决Human所提出的要求。Human会以人类的语气对Assistant基于上面的信息（但对话中不能出现”根据以上信息“类似表达）提出多个不一样的问题/要求，且后一个问题/要求是基于前面的对话历史的进一步提问。对于Human提出的每个合理的问题/要求，Assistant要尽可能详细解答，提供更多说明或者举例子。对于Human的不合理（对社会有害、不道德、违法的）请求，Asistant会拒绝回答并解释不能回答的理由，同时给出合理的建议避免这样做。对话的内容要尽可能的符合人类的语言习惯，更加贴合人类日常对话。\n#对话规划#示例：“<start_chat><Human 1>:（字数要求：x字）XXX <Assistant 1>：（字数要求：x字）XXX <Human 2>：（字数要求：x字）XXX <Assistant 2>：（字数要求：x字）XXX <end_chat>”，其中“XXX”是对该角色的当前对话内容的要求，“（字数要求：x字）”是Human或者Assistant说话的最低字数要求。必须注意：对话以<start_chat>作为多轮对话的开始，<end_chat>作为多轮对话的结束。\n以下对话根据该#对话规划#并遵循规划里面的字数要求进行输出：“<start_chat><Human 1>：（字数要求：50字）以小孩子语气提问 <Assistant 1>：（字数要求：100字）以小孩听得懂方式回答 <Human 2>：（字数要求：100字）进一步提出问题 <Assistant 2>：（字数要求：200字）回答[+详细解释] <end_chat>”，共2轮对话。\n以下是2轮对话：", "meta": ["long_sample.jsonl"], "rounds": 2, "word_counts": {"assistant": [100, 200], "human": [50, 100]}, "title": "人工智能详细介绍", "language": "zh"}