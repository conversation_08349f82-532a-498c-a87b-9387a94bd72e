# 🗣️ Speech Development & Reference-based Dialogue Datasets

A comprehensive collection of datasets for speech therapy, child language development, and reference-based dialogue generation.

## 📊 Dataset Overview

This repository contains two main types of datasets:

1. **RefGPT Reference-based Dialogue Dataset** - Multi-turn conversations generated from reference documents
2. **Speech Articulation Assessment Dataset** - Professional speech therapy evaluation data for children

## 🔬 RefGPT Reference-based Dialogue Dataset

### Dataset Description

The RefGPT dataset contains multi-turn conversations generated from reference documents using the Qwen API. Each conversation follows a structured format with controlled word counts and diverse dialogue styles.

**File:** `output-refgpt-qwen.jsonl`

### Dataset Statistics

- **Total Records:** 170 conversations
- **Languages:** English (primary), with some Chinese variants
- **Conversation Rounds:** 1-5 turns per dialogue
- **Average Length:** 400-600 words per conversation
- **Generation Method:** Qwen API with reference document conditioning

### Data Format

```json
{
  "rounds": 2,
  "word_counts": {
    "assistant": [200, 200],
    "human": [20, 20]
  },
  "dialogue": "<start_chat><Human 1>:(word count: 20 words)Question... <Assistant 1>:(word count: 200 words)Answer... <end_chat>",
  "title": "Document title (if available)",
  "reference": "Original reference document content",
  "prompt": "Generation prompt used",
  "meta": ["source_file_path"]
}
```

### Key Features

- **Reference-grounded:** All conversations are based on provided reference documents
- **Controlled generation:** Precise word count requirements for each turn
- **Diverse styles:** 11 different conversation styles (expert tone, curious questioning, etc.)
- **Multi-turn structure:** Natural conversation flow with follow-up questions
- **Quality control:** Structured format with validation

### Use Cases

- Training conversational AI models
- Reference-based question answering systems
- Multi-turn dialogue generation
- Document-grounded conversation research

## 🗣️ Speech Articulation Assessment Dataset

### Dataset Description

Professional speech therapy evaluation dataset for children aged 12-48 months, based on standardized 52-word articulation tests and clinical assessment protocols.

**Primary File:** `gouyin-1/test_gouyin_dataset.jsonl`

### Dataset Statistics

- **Total Records:** 51 assessment cases
- **Age Range:** 12-48 months (1-4 years)
- **Language:** Chinese (Mandarin)
- **Assessment Method:** 52 standardized test words
- **Professional Standard:** Based on clinical speech therapy protocols

### Data Format

```json
{
  "record_id": "GY_20250718_0001",
  "source": "gouyin_52_word_assessment",
  "basic_info": {
    "child_id": "GY8483",
    "age_months": 29,
    "age_description": "2岁5个月",
    "gender": "男",
    "cooperation_level": "需要鼓励"
  },
  "word_assessment": {
    "桌": {
      "target_phoneme": "zh",
      "actual_production": "zh+额外音",
      "result": "增加",
      "stimulability": "部分可刺激"
    }
    // ... 52 words total
  },
  "overall_results": {
    "accuracy_percentage": 55.8,
    "performance_level": "接近年龄预期",
    "intelligibility": "部分清晰",
    "severity_level": "轻度"
  },
  "recommendations": {
    "immediate_goals": ["精细化发音技能", "提高复杂词汇准确性"],
    "therapy_approaches": ["精准发音训练", "语音自我监控"],
    "home_activities": ["故事复述", "词汇扩展游戏"]
  },
  "dialogue": "Professional consultation dialogue..."
}
```

### Assessment Components

#### 52 Standardized Test Words
- **Consonants:** p, b, m, f, d, t, n, l, g, k, h, j, q, x, zh, ch, sh, r, z, c, s
- **Vowels:** a, e, i, o, u, and complex vowels
- **Tones:** All four Mandarin tones
- **Syllable structures:** Simple and complex combinations

#### Error Pattern Analysis
- **Substitution (替代):** Replacing one phoneme with another
- **Omission (省略):** Leaving out phonemes or syllables  
- **Distortion (歪曲):** Inaccurate but recognizable production
- **Addition (增加):** Adding extra sounds to correct production

#### Professional Recommendations
- **Immediate goals:** 2-3 specific therapy targets
- **Therapy approaches:** Evidence-based intervention methods
- **Home activities:** Family-friendly practice suggestions
- **Follow-up plans:** Timeline and monitoring strategies

### Age-based Performance Expectations

| Age Range | Expected Accuracy | Typical Concerns |
|-----------|------------------|------------------|
| 12-18 months | 25-45% | Late talking, limited vocabulary |
| 18-24 months | 40-60% | Unclear speech, word approximations |
| 24-36 months | 55-75% | Specific sound errors, intelligibility |
| 36-48 months | 70-90% | Complex sounds, school readiness |

### Use Cases

- **Clinical Assessment:** Standardized speech evaluation protocols
- **AI Training:** Speech therapy chatbot development
- **Research:** Child language development studies
- **Education:** Training speech-language pathologists
- **Family Support:** Parent guidance and home practice

## 🛠️ Generation Tools

### RefGPT Generator
- **Script:** `parallel_generate-refgpt-fact.py`
- **API:** Qwen (Alibaba Cloud)
- **Features:** Parallel processing, rate limiting, error handling
- **Customization:** Adjustable word counts, conversation styles, languages

### Speech Assessment Builder
- **Script:** `gouyin-1/gouyin_dataset_builder.py`
- **Source:** Clinical assessment protocols and Excel templates
- **Features:** Age-appropriate generation, professional terminology
- **Validation:** Clinical accuracy and consistency checks

## 📈 Data Quality

### RefGPT Dataset Quality
- **Coherence:** Reference-grounded responses ensure topical consistency
- **Diversity:** Multiple conversation styles and reference domains
- **Structure:** Standardized format with clear turn boundaries
- **Length Control:** Precise word count adherence

### Speech Assessment Quality
- **Clinical Accuracy:** Based on established speech therapy protocols
- **Age Appropriateness:** Developmentally accurate expectations
- **Professional Terminology:** Consistent use of clinical language
- **Practical Utility:** Actionable recommendations and guidance

## 🚀 Usage Examples

### Loading RefGPT Data
```python
import json

# Load RefGPT conversations
with open('output-refgpt-qwen.jsonl', 'r') as f:
    refgpt_data = [json.loads(line) for line in f]

# Extract dialogue content
dialogues = [item['dialogue'] for item in refgpt_data]
```

### Loading Speech Assessment Data
```python
import json

# Load speech assessment records
with open('gouyin-1/test_gouyin_dataset.jsonl', 'r', encoding='utf-8') as f:
    speech_data = [json.loads(line) for line in f]

# Extract assessment results
assessments = [item['overall_results'] for item in speech_data]
```

## 📋 Dataset Splits

### RefGPT Dataset
- **Total:** 170 conversations
- **Suggested split:** 80% train (136), 10% validation (17), 10% test (17)

### Speech Assessment Dataset  
- **Total:** 51 assessment cases
- **Age distribution:** Balanced across 12-48 month range
- **Suggested split:** 80% train (41), 20% test (10)

## 🔍 Evaluation Metrics

### RefGPT Evaluation
- **Relevance:** Alignment with reference documents
- **Coherence:** Logical conversation flow
- **Completeness:** Adherence to word count requirements
- **Diversity:** Variety in conversation styles and topics

### Speech Assessment Evaluation
- **Clinical Accuracy:** Agreement with professional standards
- **Recommendation Quality:** Appropriateness of therapy suggestions
- **Age Appropriateness:** Developmental accuracy
- **Practical Utility:** Actionability of guidance

## 📚 Citation

If you use these datasets in your research, please cite:

```bibtex
@dataset{speech_dialogue_datasets_2025,
  title={Speech Development and Reference-based Dialogue Datasets},
  author={[Your Name]},
  year={2025},
  description={Comprehensive datasets for speech therapy and reference-based dialogue generation},
  url={[Your Repository URL]}
}
```

## 📄 License

This dataset is released under [specify your license, e.g., MIT License, CC BY 4.0, etc.]

## 🤝 Contributing

We welcome contributions to improve and expand these datasets. Please see our contribution guidelines for more information.

## 📞 Contact

For questions, suggestions, or collaborations, please contact [your contact information].

---

**Keywords:** speech therapy, child language development, dialogue generation, reference-based conversation, articulation assessment, clinical data, conversational AI
