import pandas as pd

# 构建表格数据
data = [
    ["S1", "桌", "zhuō", "zh"],
    ["S2", "象", "xiàng", "iang"],
    [1, "包", "bāo", "b"],
    [2, "抛", "pāo", "p"],
    [3, "猫", "māo", "m"],
    [4, "飞", "fēi", "f"],
    [5, "刀", "dāo", "d"],
    [6, "套", "tào", "t"],
    [7, "闹", "nào", "n"],
    [8, "鹿", "lù", "l"],
    [9, "高", "gāo", "g"],
    [10, "铐", "kào", "k"],
    [11, "河", "hé", "h"],
    [12, "鸡", "jī", "J"],
    [13, "七", "qī", "Q"],
    [14, "吸", "xī", "x"],
    [15, "猪", "zhū", "zh"],
    [16, "出", "chū", "ch"],
    [17, "书", "shū", "sh"],
    [18, "肉", "ròu", "r"],
    [19, "紫", "zǐ", "z"],
    [20, "粗", "cū", "c"],
    [21, "四", "sì", "s"],
    [22, "杯", "bēi", "b"],
    [23, "泡", "pào", "p"],
    [24, "稻", "dào", "d"],
    [25, "菇", "gū", "g"],
    [26, "哭", "kū", "k"],
    [27, "壳", "ké", "k"],
    [28, "纸", "zhǐ", "zh"],
    [29, "室", "shì", "sh"],
    [30, "自", "zì", "z"],
    [31, "刺", "cì", "c"],
    [32, "蓝", "lán", "an"],
    [33, "狼", "láng", "ang"],
    [34, "心", "xīn", "in"],
    [35, "星", "xīng", "ing"],
    [36, "船", "chuán", "uan"],
    [37, "床", "chuáng", "uang"],
    [38, "拔", "bá", "a"],
    [39, "鹅", "é", "e"],
    [40, "一", "yī", "i"],
    [41, "家", "jiā", "ia"],
    [42, "浇", "jiāo", "iao"],
    [43, "乌", "wū", "u"],
    [44, "雨", "yǔ", "u"],
    [45, "椅", "yǐ", "i"],
    [46, "鼻", "bí", "i"],
    [47, "蛙", "wā", "l"],  # 这里“蛙”目标音原文表格里写的“l”，按原文处理
    [48, "娃", "wá", "2"],  # 原文“2”，按原文
    [49, "瓦", "wǎ", "3"],  # 原文“3”，按原文
    [50, "袜", "wà", "4"]   # 原文“4”，按原文
]

columns = ["序号", "词", "目标音（拼音）", "目标音"]

df = pd.DataFrame(data, columns=columns)

# 将 DataFrame 写入 Excel 文件
df.to_excel("构音语音能力评估记录表1.xlsx", index=False, engine='openpyxl')
print("Excel 文件已生成：构音语音能力评估记录表1.xlsx")