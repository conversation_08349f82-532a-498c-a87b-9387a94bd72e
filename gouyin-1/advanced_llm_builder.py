#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
高级LLM增强数据集构建器
集成多种智能功能，生成高质量、多样化的专业数据集
"""

import json
import random
import requests
import time
import asyncio
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
from llm_config import config

class AdvancedLLMBuilder:
    """高级LLM数据集构建器"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or config.QWEN_API_KEY
        self.config = config
        
        # 验证配置
        if not self.config.validate_config():
            raise ValueError("配置验证失败")
        
        # 加载词汇和知识库
        self.word_list = self.load_word_list()
        self.knowledge_base = self.build_enhanced_knowledge_base()
        
        # 统计信息
        self.stats = {
            "total_generated": 0,
            "successful": 0,
            "failed": 0,
            "api_calls": 0,
            "start_time": None
        }
    
    def load_word_list(self) -> List[Dict[str, str]]:
        """加载52个词汇列表"""
        try:
            df = pd.read_excel("构音语音能力评估记录表（all）.xlsx", 
                             sheet_name='Page1', header=None)
            
            word_list = []
            for i in range(1, min(53, len(df))):  # 确保最多52个词汇
                if len(df.columns) > 3:
                    word = str(df.iloc[i, 1]).strip() if pd.notna(df.iloc[i, 1]) else ""
                    target_phoneme = str(df.iloc[i, 3]).strip() if pd.notna(df.iloc[i, 3]) else ""
                    
                    if word and any('\u4e00' <= char <= '\u9fff' for char in word):
                        word_list.append({
                            "word": word,
                            "target_phoneme": target_phoneme,
                            "index": i,
                            "difficulty": self.assess_phoneme_difficulty(target_phoneme)
                        })
            
            print(f"✅ 成功加载 {len(word_list)} 个词汇")
            return word_list
            
        except Exception as e:
            print(f"❌ 加载词汇失败: {e}")
            return self.get_enhanced_default_words()
    
    def get_enhanced_default_words(self) -> List[Dict[str, str]]:
        """获取增强的默认词汇列表"""
        words = [
            {"word": "包", "target_phoneme": "b", "index": 1, "difficulty": "easy"},
            {"word": "抛", "target_phoneme": "p", "index": 2, "difficulty": "easy"},
            {"word": "猫", "target_phoneme": "m", "index": 3, "difficulty": "easy"},
            {"word": "飞", "target_phoneme": "f", "index": 4, "difficulty": "medium"},
            {"word": "刀", "target_phoneme": "d", "index": 5, "difficulty": "easy"},
            {"word": "套", "target_phoneme": "t", "index": 6, "difficulty": "easy"},
            {"word": "闹", "target_phoneme": "n", "index": 7, "difficulty": "easy"},
            {"word": "鹿", "target_phoneme": "l", "index": 8, "difficulty": "medium"},
            {"word": "高", "target_phoneme": "g", "index": 9, "difficulty": "easy"},
            {"word": "考", "target_phoneme": "k", "index": 10, "difficulty": "easy"},
            {"word": "河", "target_phoneme": "h", "index": 11, "difficulty": "easy"},
            {"word": "猪", "target_phoneme": "zh", "index": 12, "difficulty": "hard"},
            {"word": "出", "target_phoneme": "ch", "index": 13, "difficulty": "hard"},
            {"word": "书", "target_phoneme": "sh", "index": 14, "difficulty": "hard"},
            {"word": "肉", "target_phoneme": "r", "index": 15, "difficulty": "hard"},
            {"word": "紫", "target_phoneme": "z", "index": 16, "difficulty": "medium"},
            {"word": "粗", "target_phoneme": "c", "index": 17, "difficulty": "medium"},
            {"word": "四", "target_phoneme": "s", "index": 18, "difficulty": "medium"},
        ]
        
        # 扩展到52个词汇
        additional_words = [
            {"word": "杯", "target_phoneme": "b", "index": 19, "difficulty": "easy"},
            {"word": "泡", "target_phoneme": "p", "index": 20, "difficulty": "easy"},
            # ... 可以继续添加到52个
        ]
        
        return words + additional_words
    
    def assess_phoneme_difficulty(self, phoneme: str) -> str:
        """评估音素难度"""
        easy_phonemes = ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g"]
        medium_phonemes = ["f", "l", "s", "z", "c"]
        hard_phonemes = ["zh", "ch", "sh", "r", "j", "q", "x"]
        
        if phoneme in easy_phonemes:
            return "easy"
        elif phoneme in medium_phonemes:
            return "medium"
        elif phoneme in hard_phonemes:
            return "hard"
        else:
            return "medium"
    
    def build_enhanced_knowledge_base(self) -> Dict[str, Any]:
        """构建增强的知识库"""
        return {
            "developmental_stages": {
                "12-18": {
                    "expected_phonemes": ["p", "b", "m", "w", "h"],
                    "vocabulary_range": (10, 50),
                    "accuracy_range": (25, 45),
                    "common_concerns": ["说话晚", "只会叫爸妈", "发音不清"]
                },
                "18-24": {
                    "expected_phonemes": ["p", "b", "m", "w", "h", "n", "t", "d"],
                    "vocabulary_range": (50, 200),
                    "accuracy_range": (40, 60),
                    "common_concerns": ["词汇少", "发音模糊", "别人听不懂"]
                },
                "24-36": {
                    "expected_phonemes": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g", "f"],
                    "vocabulary_range": (200, 500),
                    "accuracy_range": (55, 75),
                    "common_concerns": ["某些音发不出", "说话不清楚", "与同龄人差距"]
                },
                "36-48": {
                    "expected_phonemes": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g", "f", "l", "s"],
                    "vocabulary_range": (500, 1000),
                    "accuracy_range": (70, 90),
                    "common_concerns": ["复杂音发不准", "准备上学担心", "与老师交流困难"]
                }
            },
            
            "error_patterns_detailed": {
                "替代": {
                    "description": "用一个音素替代另一个音素",
                    "examples": ["用/t/替代/k/", "用/d/替代/g/"],
                    "therapy": ["最小对比练习", "听觉辨别训练"]
                },
                "省略": {
                    "description": "省略某个音素或音节",
                    "examples": ["'包'说成'ao'", "'飞机'说成'机'"],
                    "therapy": ["音节意识训练", "慢速发音练习"]
                },
                "歪曲": {
                    "description": "发音不准确但仍可识别",
                    "examples": ["舌位不正确", "气流方向错误"],
                    "therapy": ["口部运动训练", "视觉提示法"]
                },
                "增加": {
                    "description": "在正确发音基础上增加额外音素",
                    "examples": ["'书'说成'书额'", "添加多余音节"],
                    "therapy": ["节拍训练", "音节分解练习"]
                }
            },
            
            "family_backgrounds": [
                {"education": "高中", "stimulation": "一般", "concerns": "基础担心"},
                {"education": "大学", "stimulation": "丰富", "concerns": "发展对比"},
                {"education": "研究生", "stimulation": "专业", "concerns": "细节关注"},
                {"education": "职业教育", "stimulation": "实用", "concerns": "功能性担心"}
            ],
            
            "therapy_methods": {
                "视觉提示法": "使用镜子、图片等视觉辅助",
                "听觉训练": "强化听觉辨别和反馈",
                "触觉提示": "通过触摸感受发音部位",
                "口部运动训练": "加强口腔肌肉协调",
                "最小对比练习": "对比相似音素的差异",
                "音素意识训练": "提高对音素的认知",
                "语音自我监控": "培养自我纠错能力"
            }
        }
    
    async def call_qwen_api_async(self, prompt: str, system_prompt: str = None, max_tokens: int = None) -> Optional[str]:
        """异步调用Qwen API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        data = {
            "model": self.config.MODEL_NAME,
            "input": {"messages": messages},
            "parameters": {
                "max_tokens": max_tokens or self.config.MAX_TOKENS,
                "temperature": self.config.TEMPERATURE,
                "top_p": self.config.TOP_P
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.config.QWEN_API_URL, 
                                      headers=headers, 
                                      json=data, 
                                      timeout=30) as response:
                    response.raise_for_status()
                    result = await response.json()
                    
                    self.stats["api_calls"] += 1
                    
                    if "output" in result and "text" in result["output"]:
                        return result["output"]["text"].strip()
                    else:
                        print(f"❌ API响应格式异常: {result}")
                        return None
                        
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            return None
    
    def generate_smart_child_profile(self, age_months: int) -> Dict[str, Any]:
        """智能生成儿童档案"""
        
        # 根据年龄确定发展阶段
        stage_key = self.get_age_stage(age_months)
        stage_info = self.knowledge_base["developmental_stages"][stage_key]
        
        # 选择合适的家庭背景
        family_bg = random.choice(self.knowledge_base["family_backgrounds"])
        
        # 生成基础档案
        profile = {
            "age_months": age_months,
            "age_description": f"{age_months // 12}岁{age_months % 12}个月",
            "gender": random.choice(["男", "女"]),
            "child_id": f"ADV{random.randint(1000, 9999)}",
            
            # 发展信息
            "first_words_age": random.randint(8, 18),
            "vocabulary_size": random.randint(*stage_info["vocabulary_range"]),
            "expected_accuracy": random.randint(*stage_info["accuracy_range"]),
            
            # 家庭背景
            "family_education": family_bg["education"],
            "language_stimulation": family_bg["stimulation"],
            "developmental_concerns": random.choice(stage_info["common_concerns"]),
            
            # 其他信息
            "language_environment": random.choice(["单语", "双语", "多语"]),
            "hearing_status": "正常",
            "oral_structure": "正常",
            "cooperation_level": random.choice(["良好", "一般", "需要鼓励", "较差"]),
            
            # 评估环境
            "assessment_date": datetime.now().strftime("%Y-%m-%d"),
            "assessor": f"评估师{random.randint(1, 10)}",
            "test_duration": f"{random.randint(20, 45)}分钟"
        }
        
        return profile
    
    def get_age_stage(self, age_months: int) -> str:
        """获取年龄阶段"""
        if 12 <= age_months < 18:
            return "12-18"
        elif 18 <= age_months < 24:
            return "18-24"
        elif 24 <= age_months < 36:
            return "24-36"
        elif 36 <= age_months <= 48:
            return "36-48"
        else:
            return "24-36"  # 默认
    
    def generate_realistic_word_assessment(self, child_profile: Dict) -> Dict[str, Dict[str, str]]:
        """生成真实的词汇评估结果"""
        
        age_months = child_profile["age_months"]
        stage_key = self.get_age_stage(age_months)
        expected_phonemes = self.knowledge_base["developmental_stages"][stage_key]["expected_phonemes"]
        
        assessment = {}
        
        for word_info in self.word_list:
            word = word_info["word"]
            target_phoneme = word_info["target_phoneme"]
            difficulty = word_info["difficulty"]
            
            # 根据音素是否在预期范围内调整正确率
            if target_phoneme in expected_phonemes:
                base_accuracy = 0.8
            elif difficulty == "easy":
                base_accuracy = 0.6
            elif difficulty == "medium":
                base_accuracy = 0.4
            else:  # hard
                base_accuracy = 0.2
            
            # 根据儿童特点调整
            if child_profile["cooperation_level"] == "良好":
                base_accuracy += 0.1
            elif child_profile["cooperation_level"] == "较差":
                base_accuracy -= 0.1
            
            if child_profile["language_stimulation"] == "丰富":
                base_accuracy += 0.05
            elif child_profile["language_stimulation"] == "较少":
                base_accuracy -= 0.05
            
            # 生成结果
            if random.random() < base_accuracy:
                result = "正确"
                actual_production = target_phoneme
                stimulability = "不适用"
            else:
                error_type = random.choice(["替代", "省略", "歪曲", "增加"])
                result = error_type
                actual_production = self.generate_error_production(target_phoneme, error_type)
                stimulability = random.choice(["可刺激", "不可刺激", "部分可刺激"])
            
            assessment[word] = {
                "target_phoneme": target_phoneme,
                "actual_production": actual_production,
                "result": result,
                "stimulability": stimulability,
                "difficulty": difficulty
            }
        
        return assessment
    
    def generate_error_production(self, target: str, error_type: str) -> str:
        """生成错误发音"""
        if error_type == "省略":
            return "省略"
        elif error_type == "增加":
            return f"{target}+额外音"
        elif error_type == "歪曲":
            return f"{target}(歪曲)"
        else:  # 替代
            substitution_map = {
                "zh": "z", "ch": "c", "sh": "s", "r": "l",
                "k": "t", "g": "d", "f": "p", "s": "t"
            }
            return substitution_map.get(target, f"{target}→其他音")
    
    async def generate_enhanced_record_async(self, age_months: int) -> Optional[Dict[str, Any]]:
        """异步生成增强记录"""
        
        try:
            # 1. 生成智能儿童档案
            child_profile = self.generate_smart_child_profile(age_months)
            
            # 2. 生成真实词汇评估
            word_assessment = self.generate_realistic_word_assessment(child_profile)
            
            # 3. 计算整体结果
            overall_results = self.calculate_overall_results(word_assessment, child_profile)
            
            # 4. 使用LLM生成专业建议
            recommendations = await self.generate_recommendations_with_llm_async(
                child_profile, overall_results
            )
            
            # 5. 使用LLM生成专业对话
            dialogue = await self.generate_dialogue_with_llm_async(
                child_profile, overall_results, recommendations
            )
            
            # 6. 组装完整记录
            record = {
                "record_id": f"ADV_{datetime.now().strftime('%Y%m%d')}_{random.randint(1000, 9999)}",
                "source": "advanced_llm_enhanced",
                "created_at": datetime.now().isoformat(),
                "child_profile": child_profile,
                "word_assessment": word_assessment,
                "overall_results": overall_results,
                "recommendations": recommendations,
                "dialogue": dialogue,
                "metadata": {
                    "word_count": len(word_assessment),
                    "llm_enhanced": True,
                    "generation_method": "advanced_async"
                }
            }
            
            return record
            
        except Exception as e:
            print(f"❌ 生成记录失败: {e}")
            return None
    
    def calculate_overall_results(self, word_assessment: Dict, child_profile: Dict) -> Dict[str, Any]:
        """计算整体评估结果"""
        
        total_words = len(word_assessment)
        correct_words = sum(1 for result in word_assessment.values() if result["result"] == "正确")
        accuracy = (correct_words / total_words * 100) if total_words > 0 else 0
        
        # 统计错误模式
        error_patterns = {"替代": 0, "省略": 0, "歪曲": 0, "增加": 0}
        problem_phonemes = []
        
        for word, result in word_assessment.items():
            if result["result"] != "正确":
                error_patterns[result["result"]] += 1
                if result["target_phoneme"] not in problem_phonemes:
                    problem_phonemes.append(result["target_phoneme"])
        
        # 确定主要错误模式
        main_error_pattern = max(error_patterns.items(), key=lambda x: x[1])[0] if sum(error_patterns.values()) > 0 else "无"
        
        # 确定表现水平
        expected_accuracy = child_profile["expected_accuracy"]
        if accuracy >= expected_accuracy:
            performance_level = "符合年龄预期"
        elif accuracy >= expected_accuracy * 0.8:
            performance_level = "接近年龄预期"
        elif accuracy >= expected_accuracy * 0.6:
            performance_level = "低于年龄预期"
        else:
            performance_level = "明显低于年龄预期"
        
        # 确定清晰度
        if accuracy >= 90:
            intelligibility = "高度清晰"
        elif accuracy >= 75:
            intelligibility = "大部分清晰"
        elif accuracy >= 50:
            intelligibility = "部分清晰"
        else:
            intelligibility = "难以理解"
        
        return {
            "total_words": total_words,
            "correct_words": correct_words,
            "accuracy_percentage": round(accuracy, 1),
            "expected_accuracy": expected_accuracy,
            "performance_level": performance_level,
            "intelligibility": intelligibility,
            "error_patterns": error_patterns,
            "main_error_pattern": main_error_pattern,
            "problem_phonemes": problem_phonemes[:5],  # 最多5个
            "severity_level": self.determine_severity(performance_level)
        }
    
    def determine_severity(self, performance_level: str) -> str:
        """确定严重程度"""
        severity_map = {
            "符合年龄预期": "正常",
            "接近年龄预期": "轻度",
            "低于年龄预期": "中度",
            "明显低于年龄预期": "重度"
        }
        return severity_map.get(performance_level, "未知")
    
    async def generate_recommendations_with_llm_async(self, child_profile: Dict, overall_results: Dict) -> Dict[str, Any]:
        """异步生成个性化建议"""
        
        system_prompt = self.config.get_system_prompts()["therapist"]
        
        prompt = self.config.get_prompt_templates()["recommendations"].format(
            age_months=child_profile["age_months"],
            accuracy_percentage=overall_results["accuracy_percentage"],
            main_error_pattern=overall_results["main_error_pattern"],
            problem_phonemes=", ".join(overall_results["problem_phonemes"]),
            performance_level=overall_results["performance_level"],
            intelligibility=overall_results["intelligibility"],
            language_environment=child_profile["language_environment"],
            family_education=child_profile["family_education"],
            language_stimulation=child_profile["language_stimulation"],
            cooperation_level=child_profile["cooperation_level"]
        )
        
        response = await self.call_qwen_api_async(prompt, system_prompt)
        
        if response:
            try:
                return json.loads(response)
            except:
                print("❌ 建议JSON解析失败，使用默认生成")
        
        # 默认生成逻辑
        return self.generate_default_recommendations(overall_results, child_profile)
    
    async def generate_dialogue_with_llm_async(self, child_profile: Dict, overall_results: Dict, recommendations: Dict) -> str:
        """异步生成专业对话"""
        
        system_prompt = self.config.get_system_prompts()["consultant"]
        
        prompt = self.config.get_prompt_templates()["dialogue"].format(
            age_description=child_profile["age_description"],
            gender=child_profile["gender"],
            developmental_concerns=child_profile["developmental_concerns"],
            accuracy_percentage=overall_results["accuracy_percentage"],
            main_error_pattern=overall_results["main_error_pattern"],
            intelligibility=overall_results["intelligibility"],
            performance_level=overall_results["performance_level"],
            problem_phonemes=", ".join(overall_results["problem_phonemes"]),
            therapy_approaches=", ".join(recommendations.get("therapy_approaches", [])[:2]),
            home_activities=", ".join(recommendations.get("home_activities", [])[:3]),
            follow_up_plan=recommendations.get("follow_up_plan", "3个月后复查")
        )
        
        response = await self.call_qwen_api_async(prompt, system_prompt, max_tokens=1500)
        
        if response and len(response) > 200:
            return response
        
        # 默认对话生成
        return self.generate_default_dialogue(child_profile, overall_results, recommendations)
    
    def generate_default_recommendations(self, overall_results: Dict, child_profile: Dict) -> Dict[str, Any]:
        """生成默认建议"""
        severity = overall_results["severity_level"]
        
        if severity == "重度":
            return {
                "immediate_goals": ["建立基础音素发音", "提高整体语音清晰度"],
                "therapy_approaches": ["密集语音治疗", "多感官刺激法", "口部运动训练"],
                "home_activities": ["语音模仿游戏", "简单词汇练习", "口部按摩", "亲子阅读"],
                "training_schedule": "每日2次，每次15-20分钟",
                "follow_up_plan": "每月复查",
                "referral_needed": True,
                "referral_type": "语音病理学家",
                "expected_improvement": "6个月以上"
            }
        else:
            return {
                "immediate_goals": ["改善特定音素发音", "提高语音准确性"],
                "therapy_approaches": ["目标音素训练", "最小对比练习", "视觉提示法"],
                "home_activities": ["故事复述", "词汇扩展游戏", "发音练习", "儿歌朗诵"],
                "training_schedule": "每日1次，每次15分钟",
                "follow_up_plan": "3个月后复查",
                "referral_needed": False,
                "referral_type": "",
                "expected_improvement": "2-3个月"
            }
    
    def generate_default_dialogue(self, child_profile: Dict, overall_results: Dict, recommendations: Dict) -> str:
        """生成默认对话"""
        return f"""<开始对话>
<人类 1>: 您好，我想了解一下我家{child_profile['age_description']}孩子的语音发展情况。
<助手 1>: 您好！根据52个词汇的测试，孩子的发音准确率为{overall_results['accuracy_percentage']}%，{overall_results['performance_level']}。
<人类 2>: 这个结果怎么样？需要特别注意什么吗？
<助手 2>: 孩子目前的语音清晰度为{overall_results['intelligibility']}，主要问题是{overall_results['main_error_pattern']}。建议重点关注{', '.join(overall_results['problem_phonemes'][:3])}这些音素。
<人类 3>: 我们在家里应该怎么练习？
<助手 3>: 建议您{', '.join(recommendations.get('home_activities', [])[:3])}。{recommendations.get('training_schedule', '每天练习15分钟')}即可。
<人类 4>: 什么时候能看到改善？
<助手 4>: 根据孩子的情况，{recommendations.get('follow_up_plan', '3个月后复查')}。预计{recommendations.get('expected_improvement', '2-3个月')}会有明显进步。
<结束对话>"""
    
    async def build_advanced_dataset_async(self, num_records: int = 100, output_file: str = "advanced_llm_dataset.jsonl"):
        """异步构建高级数据集"""
        
        print(f"🚀 开始构建高级LLM增强数据集...")
        print(f"目标记录数: {num_records}")
        print(f"使用API: {self.config.QWEN_API_URL}")
        
        self.stats["start_time"] = datetime.now()
        self.stats["total_generated"] = num_records
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(5)  # 最多5个并发请求
        
        async def generate_with_semaphore(age_months):
            async with semaphore:
                return await self.generate_enhanced_record_async(age_months)
        
        # 生成年龄列表
        age_list = [random.randint(12, 48) for _ in range(num_records)]
        
        # 并发生成记录
        tasks = [generate_with_semaphore(age) for age in age_list]
        
        with open(output_file, "w", encoding="utf8") as f:
            for i, task in enumerate(asyncio.as_completed(tasks)):
                try:
                    record = await task
                    if record:
                        f.write(json.dumps(record, ensure_ascii=False) + "\n")
                        self.stats["successful"] += 1
                    else:
                        self.stats["failed"] += 1
                    
                    if (i + 1) % 10 == 0:
                        elapsed = (datetime.now() - self.stats["start_time"]).total_seconds()
                        print(f"已完成 {i + 1}/{num_records} 条记录... "
                              f"(成功: {self.stats['successful']}, 失败: {self.stats['failed']}, "
                              f"API调用: {self.stats['api_calls']}, 用时: {elapsed:.1f}s)")
                    
                    # 控制API调用频率
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    print(f"❌ 处理第{i+1}条记录失败: {e}")
                    self.stats["failed"] += 1
        
        # 输出最终统计
        total_time = (datetime.now() - self.stats["start_time"]).total_seconds()
        success_rate = self.stats["successful"] / num_records * 100
        
        print(f"\n✅ 高级数据集构建完成！")
        print(f"输出文件: {output_file}")
        print(f"成功记录: {self.stats['successful']}")
        print(f"失败记录: {self.stats['failed']}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"API调用次数: {self.stats['api_calls']}")
        print(f"总用时: {total_time:.1f}秒")
        print(f"平均每条记录: {total_time/num_records:.1f}秒")

# 使用示例
async def main():
    # 设置API密钥
    API_KEY = "sk-89f5a10520814825ad34c7eac4533ba3"  # 请替换为实际的API密钥
    
    if API_KEY == "your-qwen-api-key-here":
        print("❌ 请先设置Qwen API密钥")
        return
    
    try:
        builder = AdvancedLLMBuilder(API_KEY)
        
        # 构建小规模测试数据集
        await builder.build_advanced_dataset_async(
            num_records=500, 
            output_file="test_advanced_dataset.jsonl"
        )
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
