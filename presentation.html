<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>婴幼儿语音构音监测文本数据集</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 80%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px;
            display: none;
            flex-direction: column;
            position: relative;
        }

        .slide.active {
            display: flex;
        }

        .slide-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #2E86AB;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.2em;
            color: #6C757D;
            font-weight: normal;
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .cover-slide {
            text-align: center;
            justify-content: center;
        }

        .cover-title {
            font-size: 3em;
            color: #2E86AB;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .cover-subtitle {
            font-size: 1.5em;
            color: #F24236;
            margin-bottom: 40px;
        }

        .cover-author {
            font-size: 1.2em;
            color: #6C757D;
            margin-top: 60px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: center;
        }

        .content-left, .content-right {
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #2E86AB, #4A90E2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .feature-list li:before {
            content: "✓";
            color: #28A745;
            font-weight: bold;
            margin-right: 10px;
            font-size: 1.2em;
        }

        .highlight-box {
            background: linear-gradient(135deg, #F24236, #FF6B6B);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
            margin: 15px 0;
            overflow-x: auto;
        }

        .navigation {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: #2E86AB;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }

        .nav-btn:hover {
            background: #1a5f7a;
        }

        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .slide-number {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #6C757D;
            font-size: 0.9em;
        }

        .chart-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6C757D;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 30px;
                width: 95%;
                height: 90%;
            }
            
            .cover-title {
                font-size: 2em;
            }
            
            .slide-title {
                font-size: 1.8em;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <div class="slide active cover-slide">
            <div class="slide-number">1 / 8</div>
            <div class="slide-content">
                <h1 class="cover-title">婴幼儿语音构音监测文本数据集构建与应用</h1>
                <h2 class="cover-subtitle">基于临床评估标准的大规模多模态数据集</h2>
                <div class="highlight-box">
                    <strong>核心成果：</strong> 1,219条高质量记录 | 100%成功率 | 15倍效率提升
                </div>
                <p class="cover-author">作者：[你的姓名] | 2025年7月</p>
            </div>
        </div>

        <!-- 研究背景 -->
        <div class="slide">
            <div class="slide-number">2 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">研究背景与动机</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">核心问题</h3>
                        <ul class="feature-list">
                            <li>5-10%儿童存在语音发育问题</li>
                            <li>全国语音病理师不足5000人</li>
                            <li>平均诊断年龄3.5岁，错过最佳干预期</li>
                            <li>专业评估费用1000-3000元/次</li>
                        </ul>
                        
                        <div class="highlight-box">
                            <strong>AI辅助的迫切需求</strong><br>
                            提高评估效率 • 降低专业门槛 • 扩大服务覆盖
                        </div>
                    </div>
                    <div class="content-right">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">5-10%</div>
                                <div class="stat-label">发病率</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">3.5岁</div>
                                <div class="stat-label">平均诊断年龄</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">5000人</div>
                                <div class="stat-label">全国专业人员</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">70-80%</div>
                                <div class="stat-label">早期干预效果</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据集构建方法 -->
        <div class="slide">
            <div class="slide-number">3 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">数据集构建方法</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">三层数据架构</h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <strong>Layer 1:</strong> 标准化构音评估数据集 (50条)<br>
                            • 基于52词汇评估表<br>
                            • 完整的临床评估流程
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <strong>Layer 2:</strong> 通用对话训练数据集 (169条)<br>
                            • RefGPT架构启发<br>
                            • 多轮对话生成
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <strong>Layer 3:</strong> 增强版语音评估数据集 (1000条)<br>
                            • 家长咨询场景模拟<br>
                            • 5维度专业评估体系
                        </div>
                    </div>
                    <div class="content-right">
                        <h3 style="color: #F24236; margin-bottom: 20px;">技术特色</h3>
                        <ul class="feature-list">
                            <li>RefGPT并发处理架构</li>
                            <li>15个并发请求优化</li>
                            <li>智能Prompt工程设计</li>
                            <li>多层质量控制机制</li>
                            <li>专业评估标准遵循</li>
                            <li>真实场景模拟生成</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 核心技术 -->
        <div class="slide">
            <div class="slide-number">4 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">核心技术：Prompt工程与并发处理</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 15px;">Prompt工程策略</h3>
                        <div class="code-block">
System Prompt: 专业语音病理学家角色
• 10年以上临床经验
• 遵循中华医学会诊疗指南
• 5维度评估体系输出

Task Prompt: 结构化评估任务
• 儿童基本信息输入
• 发音问题详细描述
• 专业评估需求明确

Output Format: 标准化输出
1. 问题判断 (4级分类)
2. 问题分析 (150字详述)
3. 年龄适宜性 (100字评估)
4. 指导建议 (200字方案)
5. 随访建议 (100字计划)
                        </div>
                    </div>
                    <div class="content-right">
                        <h3 style="color: #F24236; margin-bottom: 15px;">并发处理优化</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">15x</div>
                                <div class="stat-label">效率提升</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">0.21s</div>
                                <div class="stat-label">平均处理时间</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">100%</div>
                                <div class="stat-label">成功率</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">3.5min</div>
                                <div class="stat-label">1000条总用时</div>
                            </div>
                        </div>
                        <div class="highlight-box">
                            <strong>技术突破：</strong><br>
                            异步HTTP请求 • 智能速率限制 • 自动重试机制 • 实时状态监控
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据集规模 -->
        <div class="slide">
            <div class="slide-number">5 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">数据集规模与特征</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">总体统计</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">1,219</div>
                                <div class="stat-label">总记录数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">3.8MB</div>
                                <div class="stat-label">总文件大小</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">12-48</div>
                                <div class="stat-label">覆盖月龄</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">3个</div>
                                <div class="stat-label">数据集数量</div>
                            </div>
                        </div>
                        
                        <h3 style="color: #F24236; margin: 20px 0;">年龄段分布</h3>
                        <ul class="feature-list">
                            <li>12-18月：161条 (13.2%) - 早期词汇期</li>
                            <li>18-24月：154条 (12.6%) - 词汇爆发期</li>
                            <li>24-36月：309条 (25.4%) - 语法发展期</li>
                            <li>36-48月：376条 (30.8%) - 语音完善期</li>
                        </ul>
                    </div>
                    <div class="content-right">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">质量特征</h3>
                        <div class="chart-placeholder">
                            数据分布可视化图表
                            <br>
                            (饼图：数据集占比)
                            <br>
                            (柱状图：年龄分布)
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <h4 style="color: #28A745; margin-bottom: 10px;">错误类型分布</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                • 歪曲：43.1% (最常见)<br>
                                • 替代：30.6% (次常见)<br>
                                • 增加：18.7% (较少)<br>
                                • 省略：7.6% (最少)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 应用价值 -->
        <div class="slide">
            <div class="slide-number">6 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">应用场景与社会价值</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">核心应用场景</h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 15px;">
                            <strong>🤖 AI模型训练</strong><br>
                            • 构音评估AI系统<br>
                            • 家长咨询机器人<br>
                            • 语音发展监测工具
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 15px;">
                            <strong>🏥 临床辅助工具</strong><br>
                            • 标准化评估支持<br>
                            • 治疗方案推荐<br>
                            • 进展跟踪系统
                        </div>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                            <strong>🎓 教育培训应用</strong><br>
                            • 家长教育平台<br>
                            • 专业培训资源<br>
                            • 科研数据支持
                        </div>
                    </div>
                    <div class="content-right">
                        <h3 style="color: #F24236; margin-bottom: 20px;">社会价值量化</h3>
                        <div class="highlight-box">
                            <strong>效率提升</strong><br>
                            评估时间：60分钟 → 6分钟 (10倍)<br>
                            服务能力：10人/天 → 100人/天 (10倍)
                        </div>
                        <div class="highlight-box">
                            <strong>成本降低</strong><br>
                            评估费用：2000元 → 200元 (90%)<br>
                            专业门槛：硕士学历 → 培训即可
                        </div>
                        <div class="highlight-box">
                            <strong>覆盖扩大</strong><br>
                            服务人群：10万 → 100万+ (10倍)<br>
                            地域覆盖：一线城市 → 全国各地
                        </div>
                        <div class="highlight-box">
                            <strong>早期干预</strong><br>
                            识别年龄：3.5岁 → 2岁 (前移1.5年)<br>
                            干预覆盖率：30% → 80% (显著提升)
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文献支撑 -->
        <div class="slide">
            <div class="slide-number">7 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">理论基础与文献支撑</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">国际权威文献</h3>
                        <ul class="feature-list">
                            <li><strong>Goldman-Fristoe Test</strong> (Goldman & Fristoe, 2015)<br>
                                标准化构音评估金标准</li>
                            <li><strong>发展性语音障碍理论</strong> (Dodd, 2014)<br>
                                分类体系与诊断标准</li>
                            <li><strong>音韵过程理论</strong> (Shriberg & Kwiatkowski, 1994)<br>
                                发展规律与评估方法</li>
                        </ul>
                        
                        <h3 style="color: #F24236; margin: 20px 0;">AI技术基础</h3>
                        <ul class="feature-list">
                            <li><strong>Transformer架构</strong> (Vaswani et al., 2017)<br>
                                现代NLP技术基础</li>
                            <li><strong>Chain-of-Thought</strong> (Wei et al., 2022)<br>
                                思维链提示技术</li>
                            <li><strong>Few-shot Learning</strong> (Brown et al., 2020)<br>
                                大语言模型能力</li>
                        </ul>
                    </div>
                    <div class="content-right">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">中文语音研究</h3>
                        <ul class="feature-list">
                            <li><strong>中国儿童语音发展常模</strong><br>
                                (林宝贵等, 2018)<br>
                                首个系统性中文常模</li>
                            <li><strong>构音障碍诊疗指南</strong><br>
                                (中华医学会, 2019)<br>
                                国家级临床标准</li>
                            <li><strong>52词汇评估表</strong><br>
                                (中华医学会, 2019)<br>
                                标准化测试工具</li>
                        </ul>
                        
                        <div class="highlight-box">
                            <strong>文献统计</strong><br>
                            • 总引用文献：21篇<br>
                            • 国际权威期刊：8篇<br>
                            • 国内核心期刊：6篇<br>
                            • 临床指南标准：7篇
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
                            <strong>理论创新</strong><br>
                            将临床评估标准与AI技术结合，<br>
                            构建了首个中文婴幼儿语音<br>
                            构音监测文本数据集
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结与展望 -->
        <div class="slide">
            <div class="slide-number">8 / 8</div>
            <div class="slide-header">
                <h1 class="slide-title">总结与未来展望</h1>
            </div>
            <div class="slide-content">
                <div class="content-grid">
                    <div class="content-left">
                        <h3 style="color: #2E86AB; margin-bottom: 20px;">核心贡献</h3>
                        <div class="highlight-box">
                            <strong>🎯 数据集贡献</strong><br>
                            • 首个中文婴幼儿语音构音监测数据集<br>
                            • 1,219条高质量专业评估记录<br>
                            • 覆盖12-48个月完整年龄段<br>
                            • 基于临床标准的5维度评估体系
                        </div>
                        
                        <div class="highlight-box">
                            <strong>⚡ 技术贡献</strong><br>
                            • RefGPT启发的高并发处理架构<br>
                            • 专业Prompt工程设计方法<br>
                            • 多层质量控制验证机制<br>
                            • 15倍效率提升的技术突破
                        </div>
                        
                        <div class="highlight-box">
                            <strong>🌟 社会贡献</strong><br>
                            • 推动AI+医疗融合发展<br>
                            • 促进早期干预普及化<br>
                            • 缩小城乡医疗服务差距<br>
                            • 提升儿童语音健康水平
                        </div>
                    </div>
                    <div class="content-right">
                        <h3 style="color: #F24236; margin-bottom: 20px;">未来展望</h3>
                        
                        <h4 style="color: #28A745; margin-bottom: 10px;">短期目标 (6个月)</h4>
                        <ul class="feature-list">
                            <li>多模态数据融合 (文本+音频)</li>
                            <li>实时语音识别集成</li>
                            <li>移动端应用开发</li>
                        </ul>
                        
                        <h4 style="color: #28A745; margin: 20px 0 10px;">中期目标 (1-2年)</h4>
                        <ul class="feature-list">
                            <li>个性化评估模型优化</li>
                            <li>跨语言支持扩展</li>
                            <li>云端服务平台建设</li>
                        </ul>
                        
                        <h4 style="color: #28A745; margin: 20px 0 10px;">长期愿景 (3-5年)</h4>
                        <ul class="feature-list">
                            <li>全球化部署应用</li>
                            <li>行业标准制定推广</li>
                            <li>完善产业生态构建</li>
                        </ul>
                        
                        <div style="text-align: center; margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #2E86AB, #4A90E2); color: white; border-radius: 10px;">
                            <strong>致力于构建更智能、更普惠的<br>婴幼儿语音健康监测生态系统</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">上一页</button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">下一页</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                changeSlide(1);
            } else if (e.key === 'ArrowLeft') {
                changeSlide(-1);
            }
        });

        // 初始化
        showSlide(0);
    </script>
</body>
</html>
