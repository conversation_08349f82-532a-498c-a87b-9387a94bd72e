#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json
import re

def analyze_dialogue_content():
    """分析对话内容的主题分布"""
    
    with open("output-refgpt-qwen.jsonl", "r", encoding="utf8") as f:
        dialogues = [json.loads(line.strip()) for line in f if line.strip()]
    
    print(f"总共分析 {len(dialogues)} 个对话")
    
    # 分类统计
    speech_therapy_count = 0
    general_topics_count = 0
    speech_therapy_dialogues = []
    general_topics = []
    
    # 语音治疗相关关键词
    speech_keywords = [
        'speech', 'sound', 'phoneme', '/f/', '/b/', '/n/', '/ng/', '/y/', '/ch/', '/j/', '/r/', '/p/',
        'articulation', 'pronunciation', 'lip movement', 'tongue', 'therapy', 'pathologist',
        'cereal', 'cheerio', 'mirror', 'voice', 'air', 'mouth', 'teeth', 'lips'
    ]
    
    for i, dialogue in enumerate(dialogues):
        dialogue_text = dialogue.get('dialogue', '').lower()
        reference_text = dialogue.get('reference', '').lower()
        
        # 检查是否包含语音治疗相关内容
        is_speech_therapy = False
        found_keywords = []
        
        for keyword in speech_keywords:
            if keyword in dialogue_text or keyword in reference_text:
                is_speech_therapy = True
                found_keywords.append(keyword)
        
        if is_speech_therapy:
            speech_therapy_count += 1
            speech_therapy_dialogues.append({
                'index': i + 1,
                'keywords': found_keywords,
                'dialogue_preview': dialogue_text[:200] + '...',
                'reference': reference_text[:100] + '...' if reference_text else 'No reference'
            })
        else:
            general_topics_count += 1
            # 提取主题
            topic = extract_topic_from_dialogue(dialogue_text)
            general_topics.append({
                'index': i + 1,
                'topic': topic,
                'dialogue_preview': dialogue_text[:200] + '...'
            })
    
    # 输出分析结果
    print(f"\n📊 内容分析结果:")
    print(f"语音构音治疗相关对话: {speech_therapy_count} 个 ({speech_therapy_count/len(dialogues)*100:.1f}%)")
    print(f"其他一般性话题对话: {general_topics_count} 个 ({general_topics_count/len(dialogues)*100:.1f}%)")
    
    print(f"\n🗣️ 语音治疗相关对话详情:")
    for dialogue in speech_therapy_dialogues[:10]:  # 显示前10个
        print(f"  对话 {dialogue['index']}: 关键词 {dialogue['keywords']}")
        print(f"    内容预览: {dialogue['dialogue_preview']}")
        print(f"    参考内容: {dialogue['reference']}")
        print()
    
    print(f"\n💬 其他话题分布:")
    topic_counts = {}
    for dialogue in general_topics:
        topic = dialogue['topic']
        topic_counts[topic] = topic_counts.get(topic, 0) + 1
    
    for topic, count in sorted(topic_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {topic}: {count} 个对话")
    
    return speech_therapy_count, general_topics_count

def extract_topic_from_dialogue(dialogue_text):
    """从对话中提取主题"""
    if 'cook' in dialogue_text or 'recipe' in dialogue_text or 'meal' in dialogue_text:
        return '烹饪/饮食'
    elif 'firewall' in dialogue_text or 'network' in dialogue_text or 'security' in dialogue_text:
        return '网络安全'
    elif 'productivity' in dialogue_text or 'motivated' in dialogue_text or 'work' in dialogue_text:
        return '工作效率'
    elif 'weather' in dialogue_text or 'sunny' in dialogue_text or 'temperature' in dialogue_text:
        return '天气'
    elif 'sandwich' in dialogue_text or 'bread' in dialogue_text:
        return '食物制作'
    elif 'online learning' in dialogue_text or 'education' in dialogue_text or 'study' in dialogue_text:
        return '在线学习'
    elif 'exercise' in dialogue_text or 'health' in dialogue_text or 'fitness' in dialogue_text:
        return '健康运动'
    elif 'artificial intelligence' in dialogue_text or 'ai' in dialogue_text:
        return '人工智能'
    else:
        return '其他'

if __name__ == "__main__":
    analyze_dialogue_content()
