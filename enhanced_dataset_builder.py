#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
增强版婴幼儿语音构音监测数据集构建工具
结合Excel评估表结构和对话生成功能
"""

import json
import pandas as pd
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any

class EnhancedDatasetBuilder:
    """增强版数据集构建器"""
    
    def __init__(self, excel_file: str = "构音语音能力评估记录表.xlsx"):
        self.excel_file = excel_file
        self.excel_structure = self.analyze_excel_structure()
        
        # 基于Excel内容提取的评估维度
        self.assessment_dimensions = {
            "phoneme_contrasts": [
                "送气/不送气对比", "塞音/擦音对比", "塞音/鼻音对比",
                "前鼻韵母/后鼻韵母对比", "声调对比"
            ],
            "error_patterns": [
                "送气化", "替代送气", "塞音化", "替代塞音", "塞擦音化",
                "替代塞擦音", "鼻音化", "替代鼻音", "卷舌化", "替代卷舌",
                "二声化", "替代二声", "三声化", "替代三声", "四声化", "替代四声"
            ],
            "assessment_areas": [
                "构音语音能力", "音位识别能力", "构音清晰度", "最小音位对比"
            ]
        }
        
        # 发展里程碑（基于中文语音发展规律）
        self.developmental_milestones = {
            12: {"expected_sounds": ["p", "b", "m", "w", "h"], "clarity": 25},
            18: {"expected_sounds": ["p", "b", "m", "w", "h", "n", "t", "d"], "clarity": 50},
            24: {"expected_sounds": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g"], "clarity": 65},
            30: {"expected_sounds": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g", "f"], "clarity": 75},
            36: {"expected_sounds": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g", "f", "l"], "clarity": 85},
            42: {"expected_sounds": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g", "f", "l", "s", "sh"], "clarity": 90},
            48: {"expected_sounds": ["p", "b", "m", "w", "h", "n", "t", "d", "k", "g", "f", "l", "s", "sh", "r", "z"], "clarity": 95}
        }

    def analyze_excel_structure(self) -> Dict[str, Any]:
        """分析Excel结构"""
        try:
            excel_data = pd.read_excel(self.excel_file, sheet_name=None, header=None)
            return {
                "sheets": list(excel_data.keys()),
                "content_extracted": True
            }
        except:
            return {"sheets": [], "content_extracted": False}

    def generate_comprehensive_assessment(self, age_months: int) -> Dict[str, Any]:
        """生成综合评估记录"""
        
        # 基本信息
        basic_info = {
            "child_id": f"C{random.randint(1000, 9999)}",
            "age_months": age_months,
            "age_description": f"{age_months // 12}岁{age_months % 12}个月",
            "gender": random.choice(["男", "女"]),
            "assessment_date": datetime.now().strftime("%Y-%m-%d"),
            "assessor": f"评估师{random.randint(1, 10)}",
            "test_environment": random.choice(["安静房间", "治疗室", "家庭环境"]),
            "parent_concerns": random.choice([
                "发音不清楚", "说话晚", "别人听不懂", "某些音发不出来",
                "说话含糊", "无特殊担心", "语音发展缓慢"
            ])
        }
        
        # 获取该年龄段的发展期望
        expected_clarity = 50  # 默认值
        expected_sounds = []
        
        for milestone_age, data in self.developmental_milestones.items():
            if age_months >= milestone_age:
                expected_clarity = data["clarity"]
                expected_sounds = data["expected_sounds"]
        
        # 音素评估（基于Excel表格中的音位对比结构）
        phoneme_assessment = self.generate_phoneme_contrasts(age_months, expected_sounds)
        
        # 计算整体表现
        total_items = sum(len(contrasts) for contrasts in phoneme_assessment.values())
        correct_items = sum(
            sum(1 for result in contrasts.values() if result == "正确")
            for contrasts in phoneme_assessment.values()
        )
        
        accuracy = (correct_items / total_items * 100) if total_items > 0 else 0
        
        # 评估结果
        assessment_results = {
            "phoneme_accuracy": round(accuracy, 1),
            "expected_accuracy": expected_clarity,
            "performance_level": self.determine_performance_level(accuracy, expected_clarity),
            "intelligibility": self.determine_intelligibility(accuracy),
            "error_patterns": self.identify_error_patterns(phoneme_assessment),
            "stimulability": random.choice(["良好", "一般", "较差"]),
            "consistency": random.choice(["一致", "不一致", "部分一致"])
        }
        
        # 个性化建议
        recommendations = self.generate_recommendations(
            age_months, assessment_results, phoneme_assessment
        )
        
        return {
            "basic_info": basic_info,
            "phoneme_assessment": phoneme_assessment,
            "assessment_results": assessment_results,
            "recommendations": recommendations,
            "follow_up": {
                "next_assessment": "3个月后",
                "priority_areas": recommendations["priority_targets"][:3],
                "home_practice_frequency": "每日15-20分钟"
            }
        }

    def generate_phoneme_contrasts(self, age_months: int, expected_sounds: List[str]) -> Dict[str, Dict[str, str]]:
        """生成音位对比评估"""
        
        contrasts = {
            "送气不送气对比": {
                "p_ph": random.choice(["正确", "送气化", "替代送气"]),
                "t_th": random.choice(["正确", "送气化", "替代送气"]),
                "k_kh": random.choice(["正确", "送气化", "替代送气"])
            },
            "塞音擦音对比": {
                "t_s": random.choice(["正确", "塞音化", "替代塞音"]),
                "k_x": random.choice(["正确", "塞音化", "替代塞音"])
            },
            "塞音鼻音对比": {
                "p_m": random.choice(["正确", "鼻音化", "替代鼻音"]),
                "t_n": random.choice(["正确", "鼻音化", "替代鼻音"]),
                "k_ng": random.choice(["正确", "鼻音化", "替代鼻音"])
            },
            "前后鼻韵对比": {
                "an_ang": random.choice(["正确", "前鼻化", "后鼻化"]),
                "en_eng": random.choice(["正确", "前鼻化", "后鼻化"]),
                "in_ing": random.choice(["正确", "前鼻化", "后鼻化"])
            },
            "声调对比": {
                "一二声": random.choice(["正确", "二声化", "替代二声"]),
                "一三声": random.choice(["正确", "三声化", "替代三声"]),
                "一四声": random.choice(["正确", "四声化", "替代四声"])
            }
        }
        
        # 根据年龄调整正确率
        if age_months < 24:
            correct_prob = 0.4
        elif age_months < 36:
            correct_prob = 0.6
        else:
            correct_prob = 0.8
        
        # 随机调整部分结果
        for contrast_type in contrasts:
            for item in contrasts[contrast_type]:
                if random.random() > correct_prob:
                    # 保持原有的错误模式
                    pass
                else:
                    contrasts[contrast_type][item] = "正确"
        
        return contrasts

    def determine_performance_level(self, accuracy: float, expected: float) -> str:
        """确定表现水平"""
        if accuracy >= expected:
            return "符合年龄预期"
        elif accuracy >= expected * 0.8:
            return "接近年龄预期"
        elif accuracy >= expected * 0.6:
            return "低于年龄预期"
        else:
            return "明显低于年龄预期"

    def determine_intelligibility(self, accuracy: float) -> str:
        """确定清晰度水平"""
        if accuracy >= 90:
            return "完全清晰"
        elif accuracy >= 75:
            return "大部分清晰"
        elif accuracy >= 50:
            return "部分清晰"
        else:
            return "难以理解"

    def identify_error_patterns(self, phoneme_assessment: Dict) -> List[str]:
        """识别错误模式"""
        patterns = []
        
        for contrast_type, results in phoneme_assessment.items():
            for item, result in results.items():
                if result != "正确" and result not in patterns:
                    patterns.append(result)
        
        return patterns[:5]  # 返回最多5个错误模式

    def generate_recommendations(self, age_months: int, results: Dict, phoneme_data: Dict) -> Dict[str, List[str]]:
        """生成个性化建议"""
        
        recommendations = {
            "immediate_goals": [],
            "therapy_approaches": [],
            "home_activities": [],
            "priority_targets": [],
            "referral_suggestions": []
        }
        
        # 基于表现水平的建议
        performance = results["performance_level"]
        
        if "明显低于" in performance:
            recommendations["immediate_goals"] = [
                "建立基础音素发音", "提高整体语音清晰度", "加强口部肌肉协调"
            ]
            recommendations["therapy_approaches"] = [
                "密集语音治疗", "多感官刺激法", "口部运动训练"
            ]
            recommendations["referral_suggestions"] = ["语音病理学家", "听力检查"]
            
        elif "低于" in performance:
            recommendations["immediate_goals"] = [
                "改善特定音素发音", "提高语音准确性", "增强语音意识"
            ]
            recommendations["therapy_approaches"] = [
                "目标音素训练", "最小对比练习", "视觉提示法"
            ]
            
        else:
            recommendations["immediate_goals"] = [
                "维持当前发展水平", "预防退步", "准备更复杂音素"
            ]
            recommendations["therapy_approaches"] = [
                "定期监测", "预防性练习", "发展性活动"
            ]
        
        # 基于错误模式的建议
        error_patterns = results["error_patterns"]
        
        if "送气化" in error_patterns or "替代送气" in error_patterns:
            recommendations["priority_targets"].append("送气不送气对比训练")
            recommendations["home_activities"].append("气流练习游戏")
            
        if "塞音化" in error_patterns or "替代塞音" in error_patterns:
            recommendations["priority_targets"].append("塞音擦音对比练习")
            recommendations["home_activities"].append("摩擦音模仿游戏")
            
        if "鼻音化" in error_patterns or "替代鼻音" in error_patterns:
            recommendations["priority_targets"].append("口鼻气流控制训练")
            recommendations["home_activities"].append("鼻音塞音对比练习")
        
        # 基于年龄的建议
        if age_months < 24:
            recommendations["home_activities"].extend([
                "语音模仿游戏", "简单词汇练习", "口部按摩"
            ])
        elif age_months < 36:
            recommendations["home_activities"].extend([
                "双音节词练习", "韵律训练", "语音意识游戏"
            ])
        else:
            recommendations["home_activities"].extend([
                "句子水平练习", "复杂音素训练", "语音规则学习"
            ])
        
        return recommendations

    def create_dialogue_from_assessment(self, assessment: Dict[str, Any], dialogue_type: str = "comprehensive") -> Dict[str, Any]:
        """基于评估记录创建对话"""
        
        basic_info = assessment["basic_info"]
        results = assessment["assessment_results"]
        recommendations = assessment["recommendations"]
        
        follow_up = assessment.get("follow_up", {})

        if dialogue_type == "initial_consultation":
            return self.create_comprehensive_dialogue(basic_info, results, recommendations, follow_up)
        elif dialogue_type == "progress_review":
            return self.create_comprehensive_dialogue(basic_info, results, recommendations, follow_up)
        elif dialogue_type == "parent_guidance":
            return self.create_comprehensive_dialogue(basic_info, results, recommendations, follow_up)
        else:
            return self.create_comprehensive_dialogue(basic_info, results, recommendations, follow_up)

    def create_comprehensive_dialogue(self, basic_info: Dict, results: Dict, recommendations: Dict, follow_up: Dict = None) -> Dict[str, Any]:
        """创建综合评估对话"""
        
        dialogue = f"""<开始对话>
<人类 1>: 您好，我想了解一下我家{basic_info['age_description']}孩子的语音发展情况。最近发现{basic_info['parent_concerns']}。
<助手 1>: 您好！我来为您的孩子进行详细的构音语音能力评估。根据标准化测试，孩子的语音准确率为{results['phoneme_accuracy']}%，{results['performance_level']}。
<人类 2>: 这个结果是什么意思？孩子的情况严重吗？
<助手 2>: 孩子目前的语音清晰度为{results['intelligibility']}，主要错误模式包括{', '.join(results['error_patterns'][:3])}。根据评估结果，建议{self.get_treatment_suggestion(results['performance_level'])}。
<人类 3>: 我在家里应该怎么帮助孩子练习？
<助手 3>: 建议您{', '.join(recommendations['home_activities'][:3])}。重点关注{', '.join(recommendations['priority_targets'][:2])}。记住要保持耐心，每天练习15-20分钟即可。
<人类 4>: 大概多长时间能看到改善？
<助手 4>: 根据孩子的年龄和当前表现，预计{self.get_progress_timeline(results['performance_level'])}。我们会在{follow_up.get('next_assessment', '3个月后') if follow_up else '3个月后'}进行复查评估。
<结束对话>"""
        
        return {
            "dialogue_type": "comprehensive_assessment",
            "child_age_months": basic_info["age_months"],
            "performance_level": results["performance_level"],
            "dialogue": dialogue,
            "assessment_summary": {
                "accuracy": results["phoneme_accuracy"],
                "intelligibility": results["intelligibility"],
                "main_errors": results["error_patterns"][:3]
            }
        }

    def get_treatment_suggestion(self, performance_level: str) -> str:
        """获取治疗建议"""
        suggestions = {
            "符合年龄预期": "继续观察发展，定期复查",
            "接近年龄预期": "加强家庭练习，密切观察",
            "低于年龄预期": "开始专业语音训练",
            "明显低于年龄预期": "立即开始密集语音治疗"
        }
        return suggestions.get(performance_level, "进一步专业评估")

    def get_progress_timeline(self, performance_level: str) -> str:
        """获取进展时间线"""
        timelines = {
            "符合年龄预期": "继续正常发展即可",
            "接近年龄预期": "1-2个月内会有改善",
            "低于年龄预期": "3-6个月的训练会看到明显进步",
            "明显低于年龄预期": "需要6个月以上的持续训练"
        }
        return timelines.get(performance_level, "需要持续专业指导")

    def build_enhanced_dataset(self, num_records: int = 500, output_file: str = "enhanced_assessment_dataset.jsonl"):
        """构建增强版数据集"""
        
        print(f"🚀 构建增强版婴幼儿语音构音监测数据集...")
        print(f"目标记录数: {num_records}")
        print(f"基于Excel评估表结构: {self.excel_file}")
        
        with open(output_file, "w", encoding="utf8") as f:
            for i in range(num_records):
                # 生成年龄（重点关注12-48个月）
                age_months = random.randint(12, 48)
                
                # 生成综合评估记录
                assessment = self.generate_comprehensive_assessment(age_months)
                
                # 生成对话
                dialogue_types = ["comprehensive_assessment", "initial_consultation", "progress_review", "parent_guidance"]
                selected_type = random.choice(dialogue_types)
                
                dialogue = self.create_dialogue_from_assessment(assessment, selected_type)
                
                # 合并数据
                dataset_entry = {
                    "record_id": f"EA_{datetime.now().strftime('%Y%m%d')}_{i+1:04d}",
                    "source": "excel_based_enhanced",
                    "created_at": datetime.now().isoformat(),
                    **assessment,
                    **dialogue
                }
                
                f.write(json.dumps(dataset_entry, ensure_ascii=False) + "\n")
                
                if (i + 1) % 50 == 0:
                    print(f"已生成 {i + 1} 条记录...")
        
        print(f"✅ 增强版数据集构建完成！")
        print(f"输出文件: {output_file}")
        print(f"总记录数: {num_records}")

if __name__ == "__main__":
    builder = EnhancedDatasetBuilder()
    
    # 生成示例记录
    print("📋 生成示例评估记录:")
    sample_assessment = builder.generate_comprehensive_assessment(30)
    print(json.dumps(sample_assessment, ensure_ascii=False, indent=2)[:1500] + "...")
    
    print("\n" + "="*60)
    
    # 生成示例对话
    print("💬 生成示例对话:")
    sample_dialogue = builder.create_dialogue_from_assessment(sample_assessment, "comprehensive_assessment")
    print(json.dumps(sample_dialogue, ensure_ascii=False, indent=2))
    
    print("\n" + "="*60)
    
    # 构建小规模数据集进行测试
    print("🔧 构建测试数据集:")
    builder.build_enhanced_dataset(num_records=50, output_file="test_enhanced_dataset.jsonl")
