# 🗣️ 语音评估数据集生成指南

基于构音评估表+Qwen LLM的高效语音评估数据集生成工具

## 🎯 功能概述

这个工具可以生成**父母描述发音问题 → LLM专业判断 → 给出指导建议**的完整数据集，用于训练婴幼儿语音构音监测AI系统。

### 核心特点
- **高效并发**: 参考RefGPT的异步处理方法，支持高并发API调用
- **专业准确**: 基于52个标准化测试词汇和临床评估标准
- **真实场景**: 模拟真实的家长咨询场景
- **质量控制**: 多层验证确保数据质量

## 📋 数据生成流程

```
1. 随机选择年龄段 (12-48个月)
     ↓
2. 选择测试词汇和错误类型
     ↓  
3. 生成父母描述 (真实的担心和疑问)
     ↓
4. LLM专业评估 (判断问题+给出建议)
     ↓
5. 格式化为完整记录
```

## 🛠️ 安装和配置

### 1. 安装依赖
```bash
pip install aiohttp aiofiles pandas openpyxl
```

### 2. 设置API密钥
```bash
# 方法1: 环境变量 (推荐)
export QWEN_API_KEY="your-qwen-api-key"

# 方法2: 运行时输入
# 程序会提示输入API密钥
```

### 3. 确保评估表文件存在
```bash
# 确认文件路径
ls gouyin-1/构音语音能力评估记录表（all）.xlsx
```

## 🚀 使用方法

### 快速开始

#### 1. 测试模式 (生成单条样本)
```bash
python run_speech_assessment.py --mode test
```

**输出示例:**
```
✅ 测试样本生成成功！

📋 样本内容预览:
- 记录ID: SA_20250718_8234
- 孩子年龄: 2岁6个月
- 目标词汇: 猪
- 家长描述: 我家男孩2岁6个月了，孩子说'猪'的时候，总是发成别的音，听起来不太对。我比较担心，这需要干预吗？
- 专业评估: 1. **问题判断**：轻度问题...

💾 测试样本已保存到: test_sample.json
```

#### 2. 生成模式 (批量生成)
```bash
# 生成1000条记录
python run_speech_assessment.py --mode generate --records 1000

# 指定输出文件
python run_speech_assessment.py --mode generate --records 500 --output my_dataset.jsonl
```

#### 3. 分析模式 (数据集分析)
```bash
python run_speech_assessment.py --mode analyze --dataset speech_assessment_dataset_20250718_143022.jsonl
```

### 高级使用

#### 直接调用生成器
```python
import asyncio
from speech_assessment_llm_generator import SpeechAssessmentGenerator

async def main():
    generator = SpeechAssessmentGenerator("your-api-key")
    
    # 生成1000条记录
    successful, failed = await generator.generate_batch_records(
        num_records=1000,
        output_file="my_dataset.jsonl",
        max_concurrent=10,  # 并发数
        delay=0.5          # API调用间隔
    )
    
    print(f"成功: {successful}, 失败: {failed}")

asyncio.run(main())
```

## 📊 生成的数据格式

### 完整记录结构
```json
{
  "record_id": "SA_20250718_8234",
  "source": "speech_assessment_llm", 
  "created_at": "2025-07-18T14:30:22.123456",
  
  "child_info": {
    "age_months": 30,
    "age_description": "2岁6个月",
    "gender": "男孩"
  },
  
  "assessment_case": {
    "target_word": "猪",
    "target_phoneme": "zh", 
    "suspected_error_type": "替代",
    "parent_description": "我家男孩2岁6个月了，孩子说'猪'的时候，总是发成别的音，听起来不太对。我比较担心，这需要干预吗？",
    "concern_level": "需要注意"
  },
  
  "professional_assessment": "1. **问题判断**：轻度问题\n\n2. **问题分析**：孩子出现的是典型的卷舌音替代问题...",
  
  "dialogue": "<开始对话>\n<家长 1>: 我家男孩2岁6个月了...\n<专家 1>: 我来为您分析一下孩子的情况...\n<结束对话>",
  
  "metadata": {
    "generation_method": "llm_assisted",
    "api_model": "qwen-turbo", 
    "assessment_type": "parent_reported"
  }
}
```

### LLM专业评估格式
```
1. **问题判断**：[正常发展/轻度问题/中度问题/需要专业评估]

2. **问题分析**：详细分析发音问题的类型和可能原因

3. **年龄适宜性**：评估该问题在此年龄段是否常见

4. **指导建议**：具体的家庭练习方法和注意事项

5. **随访建议**：何时复查或寻求专业帮助
```

## 📈 性能优化

### 并发控制
- **默认并发数**: 8-10个同时请求
- **API调用间隔**: 0.5-0.6秒
- **超时设置**: 30秒
- **错误重试**: 自动处理API失败

### 生成效率
- **1000条记录**: 约15-25分钟
- **平均每条**: 1-2秒 (包含API调用)
- **成功率**: 通常 >95%

### 成本估算
- **每条记录**: 约1次API调用
- **每次调用**: 约1000-1500 tokens
- **1000条记录**: 预估成本 $5-10 (具体以阿里云定价为准)

## 🎯 数据集特点

### 年龄分布
- **12-18个月**: 观察期，轻度关注
- **18-24个月**: 发展期，适度关注  
- **24-36个月**: 关键期，需要注意
- **36-48个月**: 准备期，建议评估

### 错误类型覆盖
- **替代**: 用其他音替代目标音
- **省略**: 省略某个音素或音节
- **歪曲**: 发音不准确但可识别
- **增加**: 添加额外的音素

### 52个测试词汇
基于真实的临床评估标准，覆盖：
- **声母**: p, b, m, f, d, t, n, l, g, k, h, j, q, x, zh, ch, sh, r, z, c, s
- **韵母**: a, e, i, o, u, an, ang, in, ing, uan, uang, ia, iao, yu等
- **声调**: 四个基本声调的组合

## 🔍 质量控制

### 数据验证
- **年龄合理性**: 12-48个月范围
- **词汇准确性**: 基于52个标准词汇
- **错误类型**: 符合临床分类标准
- **LLM回复**: 格式和内容完整性检查

### 专业性保证
- **临床标准**: 基于真实的语音治疗协议
- **年龄适宜**: 符合儿童发展规律
- **实用建议**: 可操作的家庭指导
- **专业术语**: 准确的医学表达

## 📊 数据集分析

运行分析工具查看数据集统计：

```bash
python run_speech_assessment.py --mode analyze --dataset your_dataset.jsonl
```

**分析报告示例:**
```
📈 数据集统计:
- 总记录数: 1000

👶 年龄分布:
  12-18个月: 248 条 (24.8%)
  18-24个月: 251 条 (25.1%)
  24-36个月: 253 条 (25.3%)
  36-48个月: 248 条 (24.8%)

👫 性别分布:
  男孩: 503 条 (50.3%)
  女孩: 497 条 (49.7%)

🔤 最常见的测试词汇 (前10个):
  包: 23 次
  猪: 21 次
  书: 20 次
  ...

⚠️ 错误类型分布:
  替代: 267 条 (26.7%)
  省略: 251 条 (25.1%)
  歪曲: 241 条 (24.1%)
  增加: 241 条 (24.1%)
```

## 🎯 应用场景

### 1. AI模型训练
```python
# 用于训练语音评估AI
training_data = []
for record in dataset:
    training_data.append({
        "input": record["assessment_case"]["parent_description"],
        "output": record["professional_assessment"],
        "metadata": record["child_info"]
    })
```

### 2. 对话系统开发
```python
# 用于构建家长咨询对话系统
dialogue_pairs = []
for record in dataset:
    dialogue_pairs.append({
        "conversation": record["dialogue"],
        "domain": "speech_therapy",
        "age_group": record["child_info"]["age_description"]
    })
```

### 3. 评估工具开发
```python
# 用于开发自动化评估工具
assessment_cases = []
for record in dataset:
    assessment_cases.append({
        "case": record["assessment_case"],
        "expert_judgment": record["professional_assessment"],
        "child_profile": record["child_info"]
    })
```

## 🚨 注意事项

### API使用
- **密钥安全**: 不要在代码中硬编码API密钥
- **频率限制**: 遵守阿里云API调用限制
- **成本控制**: 监控API使用量和费用

### 数据质量
- **人工审核**: 建议对生成的数据进行专业审核
- **临床验证**: 邀请语音治疗师验证内容准确性
- **持续改进**: 根据反馈优化生成逻辑

### 使用限制
- **仅供研究**: 生成的数据仅用于研究和开发
- **专业指导**: 实际临床应用需要专业人士指导
- **隐私保护**: 确保数据使用符合隐私保护要求

## 🔧 故障排除

### 常见问题

**Q: API调用失败怎么办？**
A: 检查API密钥、网络连接，适当增加延迟时间

**Q: 生成速度太慢？**
A: 可以适当增加并发数，但注意API限制

**Q: 数据质量不满意？**
A: 可以调整提示词模板，或增加后处理步骤

**Q: Excel文件加载失败？**
A: 确认文件路径正确，或使用默认词汇列表

---

🚀 **开始使用这个工具，快速生成高质量的语音评估数据集！**
